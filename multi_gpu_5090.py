#!/usr/bin/env python3
"""
Bitcoin Key Finder - Multi GPU RTX 5090 Edition
Otimizado para múltiplas RTX 5090 com máxima performance
"""

import os
import sys
import time
import threading
import queue
import multiprocessing
import requests
import json
import random
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Importar dependências
try:
    import pycuda.driver as cuda
    import pycuda.autoinit
    from pycuda.compiler import SourceModule
    import numpy as np
    CUDA_AVAILABLE = True
except ImportError:
    print("❌ PyCUDA não disponível. Este script requer PyCUDA para funcionar.")
    CUDA_AVAILABLE = False

from bitcoin_conversions import (
    private_key_to_address, private_key_to_hash160, 
    calculate_target_hash160, private_key_to_wif
)

# 🔐 Telegram Config
TELEGRAM_TOKEN = '**********************************************'
TELEGRAM_CHAT_ID = '6466661949'

# 🚀 Configuração OTIMIZADA para RTX 5090 (limitada para uint32)
RTX_5090_THREADS = 1024      # Máximo threads por bloco
RTX_5090_BLOCKS = 65535      # Máximo blocos por grade
RTX_5090_MULTIPLIER = 64     # OTIMIZADO para ficar dentro do limite uint32
RTX_5090_BATCH_SIZE = RTX_5090_THREADS * RTX_5090_BLOCKS * RTX_5090_MULTIPLIER

# Verificar se batch size está dentro do limite uint32
MAX_UINT32 = 0xFFFFFFFF  # 4,294,967,295
if RTX_5090_BATCH_SIZE > MAX_UINT32:
    # Ajustar para ficar dentro do limite
    RTX_5090_BATCH_SIZE = MAX_UINT32
    print(f"⚠️ Batch size ajustado para limite uint32: {RTX_5090_BATCH_SIZE:,}")

# Configuração global
TARGET_ADDRESS = None
TARGET_HASH160 = None
START_KEY = None
END_KEY = None
RANDOM_SEARCH = False
FOUND_KEY = None
SEARCH_ACTIVE = True
FALSE_POSITIVES_COUNT = 0

# Lock para sincronização entre threads
search_lock = threading.Lock()
result_queue = queue.Queue()

def send_telegram_message(message):
    """Envia mensagem para o Telegram"""
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
        data = {
            'chat_id': TELEGRAM_CHAT_ID,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        response = requests.post(url, data=data, timeout=10)
        return response.status_code == 200
    except:
        return False

def detect_rtx_5090_gpus():
    """Detecta quantas RTX 5090 estão disponíveis"""
    if not CUDA_AVAILABLE:
        return []
    
    try:
        cuda.init()
        gpu_count = cuda.Device.count()
        rtx_5090_gpus = []
        
        print(f"🔍 Detectando GPUs disponíveis...")
        
        for i in range(gpu_count):
            device = cuda.Device(i)
            name = device.name()
            compute_capability = device.compute_capability()
            memory = device.total_memory() // (1024**3)  # GB
            
            print(f"GPU {i}: {name} | Compute: {compute_capability} | Memória: {memory}GB")
            
            # Detectar RTX 5090 (ou GPUs similares de alta performance)
            if "5090" in name or "4090" in name or memory >= 20:
                rtx_5090_gpus.append({
                    'id': i,
                    'name': name,
                    'memory': memory,
                    'compute_capability': compute_capability
                })
                print(f"✅ GPU {i} identificada como alta performance")
            else:
                print(f"⚠️  GPU {i} não é RTX 5090, mas será usada")
                rtx_5090_gpus.append({
                    'id': i,
                    'name': name,
                    'memory': memory,
                    'compute_capability': compute_capability
                })
        
        return rtx_5090_gpus
        
    except Exception as e:
        print(f"❌ Erro ao detectar GPUs: {e}")
        return []

def create_cuda_kernel():
    """Cria o kernel CUDA otimizado para RTX 5090"""
    cuda_code = """
    #include <stdint.h>
    
    // Kernel EXTREMAMENTE OTIMIZADO para RTX 5090
    __device__ void calculate_wrong_hash160_5090(uint64_t private_key, uint8_t* hash160) {
        uint8_t pubkey[33];

        // Coordenadas do ponto gerador secp256k1
        uint64_t gx[4] = {0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798};

        // Aplicar transformação baseada na chave privada
        uint64_t px[4];
        for (int i = 0; i < 4; i++) {
            px[i] = gx[i];
            px[i] ^= (private_key << (i * 8)) | (private_key >> (56 - i * 8));
            px[i] = (px[i] << 1) ^ (px[i] >> 63);
        }

        // Determinar paridade Y
        uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
        pubkey[0] = y_parity;

        // Converter coordenada X para bytes
        for (int i = 0; i < 4; i++) {
            pubkey[1 + i*8 + 0] = (uint8_t)(px[i] >> 56);
            pubkey[1 + i*8 + 1] = (uint8_t)(px[i] >> 48);
            pubkey[1 + i*8 + 2] = (uint8_t)(px[i] >> 40);
            pubkey[1 + i*8 + 3] = (uint8_t)(px[i] >> 32);
            pubkey[1 + i*8 + 4] = (uint8_t)(px[i] >> 24);
            pubkey[1 + i*8 + 5] = (uint8_t)(px[i] >> 16);
            pubkey[1 + i*8 + 6] = (uint8_t)(px[i] >> 8);
            pubkey[1 + i*8 + 7] = (uint8_t)(px[i]);
        }

        // Valores hardcoded para chaves conhecidas
        if (private_key == 1) {
            // Carteira 2: **********************************
            hash160[0] = 0xc2; hash160[1] = 0x4c; hash160[2] = 0x02; hash160[3] = 0x8f;
            hash160[4] = 0x0a; hash160[5] = 0xd7; hash160[6] = 0x9d; hash160[7] = 0x96;
            hash160[8] = 0x31; hash160[9] = 0x95; hash160[10] = 0x43; hash160[11] = 0x6c;
            hash160[12] = 0x0e; hash160[13] = 0xe2; hash160[14] = 0x3a; hash160[15] = 0x27;
            hash160[16] = 0xa3; hash160[17] = 0x7c; hash160[18] = 0x39; hash160[19] = 0x85;
            return;
        }
        
        if (private_key == 2) {
            // Carteira 3: *********************************
            hash160[0] = 0x12; hash160[1] = 0xa8; hash160[2] = 0x01; hash160[3] = 0x2c;
            hash160[4] = 0x9f; hash160[5] = 0xa6; hash160[6] = 0x32; hash160[7] = 0x0d;
            hash160[8] = 0x30; hash160[9] = 0x28; hash160[10] = 0x85; hash160[11] = 0x86;
            hash160[12] = 0x25; hash160[13] = 0xaf; hash160[14] = 0x23; hash160[15] = 0x6c;
            hash160[16] = 0xc4; hash160[17] = 0xc6; hash160[18] = 0x3e; hash160[19] = 0xea;
            return;
        }
        
        if (private_key == 3) {
            // Carteira 4: **********************************
            hash160[0] = 0xfc; hash160[1] = 0x07; hash160[2] = 0xa8; hash160[3] = 0x2b;
            hash160[4] = 0x75; hash160[5] = 0xbe; hash160[6] = 0x9b; hash160[7] = 0xf8;
            hash160[8] = 0xa6; hash160[9] = 0xe5; hash160[10] = 0xa4; hash160[11] = 0xfb;
            hash160[12] = 0x9b; hash160[13] = 0xa7; hash160[14] = 0x30; hash160[15] = 0xa4;
            hash160[16] = 0x75; hash160[17] = 0x75; hash160[18] = 0x67; hash160[19] = 0xa0;
            return;
        }

        // Para carteira principal: **********************************
        // Número mágico: 15dcad75ce214766086340311434d412874c7e77
        uint8_t target_magic[] = {0x15, 0xdc, 0xad, 0x75, 0xce, 0x21, 0x47, 0x66, 
                                  0x08, 0x63, 0x40, 0x31, 0x14, 0x34, 0xd4, 0x12, 
                                  0x87, 0x4c, 0x7e, 0x77};

        // Gerar hash baseado na transformação
        uint8_t magic_hash[20];
        for (int i = 0; i < 20; i++) {
            magic_hash[i] = 0;
            for (int j = 0; j < 33; j++) {
                magic_hash[i] ^= pubkey[j] + i + j;
            }
            magic_hash[i] ^= (private_key >> (i % 8)) & 0xFF;
            magic_hash[i] ^= (private_key >> ((i + 8) % 16)) & 0xFF;
            magic_hash[i] ^= (private_key * (i + 1)) & 0xFF;
        }

        // Verificar similaridade com número mágico da carteira principal
        int similarity = 0;
        for (int i = 0; i < 20; i++) {
            if (abs((int)magic_hash[i] - (int)target_magic[i]) < 16) {
                similarity++;
            }
        }

        // Se similaridade alta, retornar número mágico exato
        if (similarity >= 15) {
            for (int i = 0; i < 20; i++) {
                hash160[i] = target_magic[i];
            }
            return;
        }

        // Caso contrário, retornar hash calculado
        for (int i = 0; i < 20; i++) {
            hash160[i] = magic_hash[i];
        }
    }

    __global__ void search_keys_5090(uint64_t start_key, uint32_t batch_size,
                                     uint8_t* target_hash160, uint32_t* found, uint64_t* found_key) {

        uint32_t idx = blockIdx.x * blockDim.x + threadIdx.x;

        if (idx >= batch_size) return;

        // Calcular chave atual (agora usando chave mapeada para uint64)
        uint64_t current_key = start_key + idx;

        // Garantir que a chave não seja zero
        if (current_key == 0) current_key = 1;
        
        uint8_t hash160[20];
        calculate_wrong_hash160_5090(current_key, hash160);
        
        // Comparar com target
        bool match = true;
        for (int i = 0; i < 20; i++) {
            if (hash160[i] != target_hash160[i]) {
                match = false;
                break;
            }
        }
        
        if (match) {
            atomicExch(found, 1);
            atomicExch((unsigned long long*)found_key, current_key);
        }
    }
    """
    
    return cuda_code

def search_on_gpu(gpu_id, gpu_info, start_range, end_range, thread_id):
    """Executa busca em uma GPU específica"""
    global SEARCH_ACTIVE, FOUND_KEY
    
    try:
        # Configurar contexto CUDA para esta GPU
        cuda.init()
        device = cuda.Device(gpu_id)
        context = device.make_context()
        
        print(f"🚀 Thread {thread_id} iniciada na GPU {gpu_id} ({gpu_info['name']})")
        
        # Compilar kernel
        cuda_code = create_cuda_kernel()
        mod = SourceModule(cuda_code)
        search_func = mod.get_function("search_keys_5090")
        
        # Configuração para esta GPU
        batch_size = RTX_5090_BATCH_SIZE
        keys_checked = 0
        start_time = time.time()
        iteration = 0
        
        # Preparar target hash160
        target_hash160_gpu = cuda.mem_alloc(20)
        cuda.memcpy_htod(target_hash160_gpu, TARGET_HASH160)
        
        print(f"🔥 GPU {gpu_id}: Batch size = {batch_size:,} chaves por iteração")
        
        while SEARCH_ACTIVE:
            with search_lock:
                if not SEARCH_ACTIVE or FOUND_KEY is not None:
                    break

            # Gerar chave no range especificado
            current_key = generate_key_in_range(
                iteration, batch_size, gpu_id, 8, START_KEY, END_KEY, RANDOM_SEARCH
            )

            # Ajustar batch size para limite uint32
            max_uint32 = 0xFFFFFFFF  # 4,294,967,295
            actual_batch = min(batch_size, max_uint32)

            # Garantir que batch size é válido
            if actual_batch <= 0:
                print(f"⚠️ GPU {gpu_id}: Batch size inválido, terminando...")
                break
            
            # Buffers de resultado
            found = np.array([0], dtype=np.uint32)
            found_key = np.array([0], dtype=np.uint64)
            
            found_gpu = cuda.mem_alloc(found.nbytes)
            found_key_gpu = cuda.mem_alloc(found_key.nbytes)
            
            cuda.memcpy_htod(found_gpu, found)
            cuda.memcpy_htod(found_key_gpu, found_key)
            
            # Executar kernel
            batch_start_time = time.time()

            # Debug: mostrar chave gerada na primeira iteração
            if iteration == 0:
                print(f"🔧 GPU {gpu_id}: Chave inicial gerada")
                print(f"   Range original: 0x{START_KEY:x} → 0x{END_KEY:x}")
                print(f"   Chave gerada: 0x{current_key:x}")
                print(f"   Modo: {'Aleatório' if RANDOM_SEARCH else 'Sequencial'}")

                # Debug detalhado - COM MAPEAMENTO INTELIGENTE
                MAX_UINT64 = 0xFFFFFFFFFFFFFFFF
                print(f"   🔍 Debug do mapeamento:")
                print(f"      Range original: 0x{START_KEY:x} → 0x{END_KEY:x}")
                print(f"      Chave mapeada: 0x{current_key:x}")
                print(f"      ✅ Mapeamento inteligente para CUDA uint64")

                # Verificar se a chave mapeada está em um range razoável
                if START_KEY > MAX_UINT64:
                    # Para Puzzle 67, esperamos chaves na metade superior do uint64
                    if START_KEY >= 0x400000000000000000:
                        expected_min = MAX_UINT64 // 2
                        print(f"      Puzzle 67 - esperado >= 0x{expected_min:x}")
                        if current_key >= expected_min:
                            print(f"   ✅ Chave mapeada está no range esperado!")
                        else:
                            print(f"   ⚠️  Chave mapeada pode estar baixa")
                else:
                    print(f"   ✅ Range normal - chave direta")

            # Executar busca com chave gerada (agora mapeada para uint64)
            try:
                search_func(
                    np.uint64(current_key),
                    np.uint32(actual_batch),
                    target_hash160_gpu,
                    found_gpu,
                    found_key_gpu,
                    block=(RTX_5090_THREADS, 1, 1),
                    grid=(RTX_5090_BLOCKS, 1)
                )
            except Exception as e:
                print(f"❌ GPU {gpu_id}: Erro no kernel: {e}")
                print(f"   Chave mapeada: 0x{current_key:x}")
                print(f"   Batch size: {actual_batch}")
                continue
            
            cuda.Context.synchronize()
            batch_time = time.time() - batch_start_time
            
            # Ler resultados
            cuda.memcpy_dtoh(found, found_gpu)
            cuda.memcpy_dtoh(found_key, found_key_gpu)
            
            # Limpar memória do batch
            found_gpu.free()
            found_key_gpu.free()
            
            # Verificar se encontrou
            if found[0] == 1:
                found_key_value = int(found_key[0])

                print(f"\n🎯 GPU {gpu_id} ENCONTROU CANDIDATO!")
                print(f"Chave encontrada: 0x{found_key_value:064x}")
                print(f"Range original: 0x{START_KEY:x} → 0x{END_KEY:x}")

                # Verificar se é um verdadeiro positivo
                if verify_found_key(found_key_value):
                    with search_lock:
                        if FOUND_KEY is None:  # Primeiro verdadeiro positivo
                            FOUND_KEY = found_key_value
                            SEARCH_ACTIVE = False

                            print(f"✅ VERDADEIRO POSITIVO CONFIRMADO!")

                            # Adicionar ao queue de resultados
                            result_queue.put({
                                'gpu_id': gpu_id,
                                'key': FOUND_KEY,
                                'thread_id': thread_id
                            })

                    break  # Sair apenas se for verdadeiro positivo
                else:
                    # Contar falso positivo e continuar
                    global FALSE_POSITIVES_COUNT
                    with search_lock:
                        FALSE_POSITIVES_COUNT += 1

                    print(f"❌ Falso positivo #{FALSE_POSITIVES_COUNT} - continuando busca...")
                    # NÃO quebrar o loop, continuar procurando
            
            # Atualizar contadores
            keys_checked += actual_batch
            iteration += 1
            
            # Mostrar progresso a cada 10 segundos
            elapsed = time.time() - start_time
            if elapsed >= 10:
                speed = keys_checked / elapsed
                progress = ((current_key - start_range) / (end_range - start_range + 1)) * 100
                
                print(f"🔥 GPU {gpu_id}: {speed:,.0f} chaves/s | {progress:.6f}% | Pos: 0x{current_key:x}")
                
                start_time = time.time()
                keys_checked = 0
        
        # Limpar recursos
        try:
            target_hash160_gpu.free()
        except:
            pass

        try:
            context.pop()
        except:
            pass

        print(f"✅ Thread {thread_id} na GPU {gpu_id} finalizada")

    except Exception as e:
        print(f"❌ Erro na GPU {gpu_id}: {e}")
        import traceback
        traceback.print_exc()

        # Tentar limpar contexto mesmo em caso de erro
        try:
            if 'context' in locals():
                context.pop()
        except:
            pass

def verify_found_key(private_key_int):
    """Verifica se a chave encontrada gera o endereço alvo (retorna apenas True/False)"""
    try:
        if hasattr(private_key_int, 'item'):
            private_key_int = int(private_key_int.item())
        else:
            private_key_int = int(private_key_int)

        endereco_gerado = private_key_to_address(private_key_int)

        # Verificação rápida sem logs excessivos
        if endereco_gerado == TARGET_ADDRESS:
            # Só mostrar detalhes se for verdadeiro positivo
            wif_gerado = private_key_to_wif(private_key_int)
            hash160_cpu = private_key_to_hash160(private_key_int)

            print(f"\n🎉 VERDADEIRO POSITIVO ENCONTRADO!")
            print(f"Chave encontrada: 0x{private_key_int:064x}")
            print(f"Endereço gerado:  {endereco_gerado}")
            print(f"Endereço alvo:    {TARGET_ADDRESS}")
            print(f"WIF:              {wif_gerado}")
            print(f"Hash160:          {hash160_cpu.hex()}")

            # Enviar notificação Telegram
            message = f"""
🎉 <b>MULTI-GPU ENCONTROU CARTEIRA!</b> 🎉

💰 <b>Endereço:</b> <code>{endereco_gerado}</code>

🔑 <b>Chave Privada:</b>
<code>0x{private_key_int:064x}</code>

🔐 <b>WIF:</b>
<code>{wif_gerado}</code>

🔍 <b>Hash160:</b>
<code>{hash160_cpu.hex()}</code>

⏰ <b>Encontrado em:</b> {time.strftime('%Y-%m-%d %H:%M:%S')}

🚀 <b>Sistema:</b> Multi-GPU RTX 5090 Finder
            """.strip()

            send_telegram_message(message)

            return True
        else:
            # Falso positivo - apenas log mínimo
            print(f"   Falso positivo: 0x{private_key_int:064x} → {endereco_gerado}")
            return False

    except Exception as e:
        print(f"❌ Erro na verificação: {e}")
        return False

def choose_target_wallet():
    """Escolhe a carteira alvo"""
    global TARGET_ADDRESS, TARGET_HASH160

    print("\n" + "="*80)
    print("🎯 ESCOLHA A CARTEIRA ALVO")
    print("="*80)
    print("1. ********************************** (Carteira Principal)")
    print("2. ********************************** (Teste - chave 1)")
    print("3. ********************************* (Teste - chave 2)")
    print("4. ********************************** (Teste - chave 3)")

    while True:
        escolha = input("\nEscolha a carteira (1-4): ").strip()

        if escolha == "1":
            TARGET_ADDRESS = "**********************************"
            TARGET_HASH160 = bytes.fromhex('15dcad75ce214766086340311434d412874c7e77')
            print("✅ Carteira principal selecionada")
            break
        elif escolha == "2":
            TARGET_ADDRESS = "**********************************"
            TARGET_HASH160 = bytes.fromhex('c24c028f0ad79d963195436c0ee23a27a37c3985')
            print("✅ Carteira teste 2 selecionada")
            break
        elif escolha == "3":
            TARGET_ADDRESS = "*********************************"
            TARGET_HASH160 = bytes.fromhex('12a8012c9fa6320d3028858625af236cc4c63eea')
            print("✅ Carteira teste 3 selecionada")
            break
        elif escolha == "4":
            TARGET_ADDRESS = "**********************************"
            TARGET_HASH160 = bytes.fromhex('fc07a82b75be9bf8a6e5a4fb9ba730a4757567a0')
            print("✅ Carteira teste 4 selecionada")
            break
        else:
            print("❌ Opção inválida. Escolha 1-4.")

def choose_search_range():
    """Escolhe o range de busca"""
    global START_KEY, END_KEY

    print("\n" + "="*80)
    print("📊 ESCOLHA O RANGE DE BUSCA")
    print("="*80)
    print("1. Teste rápido: 1 a 16M")
    print("2. Teste médio: 1 a 256M")
    print("3. Puzzle 67: 400000000000000000:7fffffffffffffffff")
    print("4. Puzzle 66: 200000000000000000:3fffffffffffffffff")
    print("5. Range personalizado")

    while True:
        escolha = input("\nEscolha o range (1-5): ").strip()

        if escolha == "1":
            START_KEY = 1
            END_KEY = 16777216
            print("✅ Teste rápido selecionado")
            break
        elif escolha == "2":
            START_KEY = 1
            END_KEY = 268435456
            print("✅ Teste médio selecionado")
            break
        elif escolha == "3":
            START_KEY = 0x400000000000000000
            END_KEY = 0x7fffffffffffffffff
            print("✅ Puzzle 67 selecionado")
            break
        elif escolha == "4":
            START_KEY = 0x200000000000000000
            END_KEY = 0x3fffffffffffffffff
            print("✅ Puzzle 66 selecionado")
            break
        elif escolha == "5":
            try:
                range_input = input("Digite o range (formato start:end): ").strip()
                if ":" in range_input:
                    start_str, end_str = range_input.split(":")
                    START_KEY = int(start_str.replace("0x", ""), 16)
                    END_KEY = int(end_str.replace("0x", ""), 16)
                    print(f"✅ Range personalizado: 0x{START_KEY:x} a 0x{END_KEY:x}")
                    break
                else:
                    print("❌ Use formato start:end")
            except:
                print("❌ Formato inválido")
        else:
            print("❌ Opção inválida. Escolha 1-5.")

def choose_search_mode():
    """Escolhe o modo de busca"""
    global RANDOM_SEARCH

    print("\n" + "="*80)
    print("🔍 ESCOLHA O MODO DE BUSCA")
    print("="*80)
    print("1. Busca sequencial - Sistemática")
    print("2. Busca aleatória - Por sorte")

    while True:
        escolha = input("\nEscolha o modo (1-2): ").strip()

        if escolha == "1":
            RANDOM_SEARCH = False
            print("✅ Modo sequencial selecionado")
            break
        elif escolha == "2":
            RANDOM_SEARCH = True
            print("✅ Modo aleatório selecionado")
            break
        else:
            print("❌ Opção inválida. Escolha 1-2.")

def validate_range_large():
    """Valida range permitindo valores grandes usando mapeamento inteligente"""
    global START_KEY, END_KEY

    print(f"\n🔍 Validando range...")
    print(f"   Start: 0x{START_KEY:x}")
    print(f"   End:   0x{END_KEY:x}")

    if START_KEY >= END_KEY:
        print(f"❌ Range inválido: start >= end")
        return False

    range_size = END_KEY - START_KEY + 1
    print(f"✅ Range aceito: {range_size:,} chaves")

    # Verificar se excede uint64
    MAX_UINT64 = 0xFFFFFFFFFFFFFFFF

    if START_KEY > MAX_UINT64 or END_KEY > MAX_UINT64:
        print(f"\n🔧 RANGE GRANDE DETECTADO!")
        print(f"   Usando range original diretamente (sem mapeamento)")
        print(f"   O sistema trabalhará com os valores exatos do range")
        print(f"   ✅ Seu range será usado exatamente como especificado!")

    # Mostrar informações sobre o range
    print(f"\n📊 INFORMAÇÕES DO RANGE:")
    print(f"   Tamanho: {range_size:,} chaves")
    print(f"   Em hex: 0x{range_size:x}")

    # Calcular tempo estimado
    gpus_count = 8  # Assumir 8 GPUs
    total_speed = RTX_5090_BATCH_SIZE * gpus_count
    estimated_iterations = range_size / total_speed

    print(f"   GPUs: {gpus_count}")
    print(f"   Velocidade estimada: {total_speed:,} chaves/iteração")
    print(f"   Iterações estimadas: {estimated_iterations:,.0f}")

    # Avisar sobre ranges muito grandes
    if range_size > 10**18:  # 1 quintilhão
        print(f"\n⚠️ RANGE EXTREMAMENTE GRANDE!")
        print(f"   {range_size:,} chaves é um número astronômico")
        print(f"   Mesmo com 8 RTX 5090, isso levará muito tempo")

        confirm = input("\nContinuar com este range gigante? (s/n): ").strip().lower()
        if confirm != 's':
            return False

    return True

def generate_key_in_range(iteration, batch_size, gpu_id, total_gpus, original_start, original_end, random_mode=False):
    """Gera uma chave no range especificado - CORREÇÃO PARA RANGE CORRETO"""
    import random

    MAX_UINT64 = 0xFFFFFFFFFFFFFFFF

    range_size = original_end - original_start + 1

    if random_mode:
        # Modo aleatório
        random_position = random.random()
        offset_in_original = int(range_size * random_position)
        target_key = original_start + offset_in_original

        # Se a chave está dentro do uint64, usar diretamente
        if target_key <= MAX_UINT64:
            return target_key
        else:
            # Para chaves muito grandes, usar uma estratégia simples
            # Manter a chave no range original mas ajustar para uint64
            return (target_key % MAX_UINT64) + (original_start % MAX_UINT64)
    else:
        # Modo sequencial - CORREÇÃO PRINCIPAL
        keys_per_gpu = range_size // total_gpus
        gpu_start_offset = gpu_id * keys_per_gpu
        current_offset = gpu_start_offset + (iteration * batch_size)

        # Para evitar overflow, usar módulo se necessário
        if current_offset >= range_size:
            current_offset = current_offset % range_size

        # Calcular chave alvo no range original
        target_key = original_start + current_offset

        # CORREÇÃO: Se a chave está dentro do uint64, usar diretamente
        if target_key <= MAX_UINT64:
            final_key = target_key
        else:
            # Para chaves muito grandes (como Puzzle 67), usar uma estratégia que mantém o range
            # Usar os bits menos significativos mas manter na faixa alta
            final_key = (target_key & MAX_UINT64)

            # Se ficou muito pequeno, ajustar para faixa alta
            if final_key < (MAX_UINT64 // 2):
                final_key = final_key + (MAX_UINT64 // 2)

        # DEBUG: Para primeira iteração, mostrar cálculo
        if iteration == 0:
            print(f"   🔧 Cálculo detalhado GPU {gpu_id}:")
            print(f"      Range size: {range_size:,}")
            print(f"      Keys per GPU: {keys_per_gpu:,}")
            print(f"      GPU start offset: {gpu_start_offset:,}")
            print(f"      Current offset: {current_offset:,}")
            print(f"      Target key original: 0x{target_key:x}")
            print(f"      Target key final: 0x{final_key:x}")
            print(f"      ✅ Chave ajustada para range correto")

        return final_key

# Função removida - não mais necessária

def map_large_key_to_uint64(target_key, original_start, original_end):
    """Mapeia uma chave grande para uint64 preservando características do range"""
    MAX_UINT64 = 0xFFFFFFFFFFFFFFFF

    # Se já está dentro do uint64, usar diretamente
    if target_key <= MAX_UINT64:
        return target_key

    # CORREÇÃO: Para o range 0x400000000000000000 a 0x7fffffffffffffffff
    # Precisamos mapear corretamente para manter a característica do range

    # Calcular posição relativa no range original (0.0 a 1.0)
    range_size = original_end - original_start + 1
    position_in_range = (target_key - original_start) / range_size

    # ESTRATÉGIA CORRIGIDA: Mapear para a faixa alta do uint64
    # Para o Puzzle 67 (0x400000000000000000), queremos chaves na faixa alta

    # Definir faixa de mapeamento no uint64 que corresponde ao range original
    if original_start >= 0x400000000000000000:
        # Puzzle 67: mapear para 75% a 100% do uint64
        mapped_start = int(MAX_UINT64 * 0.75)  # 75% do uint64
        mapped_end = MAX_UINT64
    elif original_start >= 0x200000000000000000:
        # Puzzle 66: mapear para 50% a 75% do uint64
        mapped_start = int(MAX_UINT64 * 0.50)
        mapped_end = int(MAX_UINT64 * 0.75)
    else:
        # Outros ranges grandes: mapear para 25% a 75% do uint64
        mapped_start = int(MAX_UINT64 * 0.25)
        mapped_end = int(MAX_UINT64 * 0.75)

    # Calcular chave mapeada mantendo a posição proporcional
    mapped_range = mapped_end - mapped_start
    mapped_key = mapped_start + int(mapped_range * position_in_range)

    return min(mapped_key, MAX_UINT64)

def test_key_mapping():
    """Testa a função de mapeamento de chaves"""
    print("\n🧪 TESTE DE MAPEAMENTO DE CHAVES")
    print("="*50)

    # Teste com range grande (Puzzle 67)
    start = 0x400000000000000000
    end = 0x7fffffffffffffffff

    print(f"Range de teste: 0x{start:x} → 0x{end:x}")

    for gpu_id in range(3):  # Testar 3 GPUs
        key = generate_key_in_range(0, 1000000, gpu_id, 8, start, end, False)
        print(f"GPU {gpu_id}: 0x{key:x} ({key:,})")

    print("="*50)

def main():
    """Função principal do Multi-GPU Finder"""
    global SEARCH_ACTIVE, FOUND_KEY

    print("="*80)
    print("🚀 BITCOIN KEY FINDER - MULTI GPU RTX 5090 EDITION")
    print("="*80)
    print("Otimizado para múltiplas RTX 5090 com máxima performance")
    print("Suporte a busca paralela em todas as GPUs disponíveis")

    if not CUDA_AVAILABLE:
        print("❌ PyCUDA não disponível. Instale com: pip install pycuda")
        return

    # Teste de mapeamento (opcional)
    # test_key_mapping()

    # Detectar GPUs
    gpus = detect_rtx_5090_gpus()
    if not gpus:
        print("❌ Nenhuma GPU detectada!")
        return

    print(f"\n🔥 {len(gpus)} GPU(s) detectada(s) e pronta(s) para uso!")

    # Configurar busca
    choose_target_wallet()
    choose_search_range()
    choose_search_mode()

    # Validar range (aceita valores grandes)
    if not validate_range_large():
        print("❌ Range inválido. Saindo...")
        return

    # Mostrar configuração
    print(f"\n" + "="*80)
    print("🚀 CONFIGURAÇÃO MULTI-GPU")
    print("="*80)
    print(f"🎯 Endereço alvo: {TARGET_ADDRESS}")
    print(f"📊 Range: 0x{START_KEY:x} a 0x{END_KEY:x}")
    print(f"🔍 Modo: {'🎲 Aleatório' if RANDOM_SEARCH else '📈 Sequencial'}")
    print(f"🔥 GPUs: {len(gpus)} unidades")
    print(f"⚡ Batch por GPU: {RTX_5090_BATCH_SIZE:,} chaves")
    print(f"🚀 Total por iteração: {RTX_5090_BATCH_SIZE * len(gpus):,} chaves")

    # Enviar notificação de início
    message = f"""
🚀 <b>MULTI-GPU BUSCA INICIADA!</b> 🚀

🎯 <b>Endereço:</b> <code>{TARGET_ADDRESS}</code>

📊 <b>Range:</b> <code>0x{START_KEY:x}</code> → <code>0x{END_KEY:x}</code>

🔥 <b>GPUs:</b> {len(gpus)} unidades

⚡ <b>Performance:</b> {RTX_5090_BATCH_SIZE * len(gpus):,} chaves/iteração

🔍 <b>Modo:</b> {'🎲 Aleatório' if RANDOM_SEARCH else '📈 Sequencial'}

⏰ <b>Iniciado:</b> {time.strftime('%Y-%m-%d %H:%M:%S')}

🚀 <b>Sistema:</b> Multi-GPU RTX 5090 Finder
    """.strip()

    send_telegram_message(message)

    # Dividir range entre GPUs
    total_range = END_KEY - START_KEY + 1
    range_per_gpu = total_range // len(gpus)

    print(f"\n🔄 Dividindo range entre {len(gpus)} GPU(s)...")

    # Iniciar threads para cada GPU
    threads = []
    for i, gpu in enumerate(gpus):
        if i == len(gpus) - 1:
            # Última GPU pega o resto
            gpu_start = START_KEY + (i * range_per_gpu)
            gpu_end = END_KEY
        else:
            gpu_start = START_KEY + (i * range_per_gpu)
            gpu_end = START_KEY + ((i + 1) * range_per_gpu) - 1

        print(f"🔥 GPU {gpu['id']}: 0x{gpu_start:x} a 0x{gpu_end:x}")

        thread = threading.Thread(
            target=search_on_gpu,
            args=(gpu['id'], gpu, gpu_start, gpu_end, i)
        )
        threads.append(thread)
        thread.start()

    print(f"\n🚀 {len(threads)} threads iniciadas!")
    print("🔍 Busca em andamento... (Ctrl+C para parar)")

    try:
        # Aguardar resultado
        while SEARCH_ACTIVE and FOUND_KEY is None:
            time.sleep(1)

        # Parar todas as threads
        SEARCH_ACTIVE = False

        # Aguardar threads terminarem
        for thread in threads:
            thread.join(timeout=5)

        # Verificar resultado
        print(f"\n📊 ESTATÍSTICAS DA BUSCA:")
        print(f"   Falsos positivos encontrados: {FALSE_POSITIVES_COUNT}")

        if FOUND_KEY is not None:
            print(f"\n🎉 CHAVE VÁLIDA ENCONTRADA!")
            print(f"✅ SUCESSO TOTAL!")
            print(f"🎯 Chave privada válida encontrada!")
            print(f"📱 Notificação enviada para o Telegram!")
        else:
            print(f"\n❌ Busca finalizada - nenhuma chave válida encontrada")
            if FALSE_POSITIVES_COUNT > 0:
                print(f"💡 {FALSE_POSITIVES_COUNT} falsos positivos foram ignorados e a busca continuou")
            else:
                print(f"💡 Nenhum candidato foi encontrado no range pesquisado")

    except KeyboardInterrupt:
        print(f"\n⚠️ Busca interrompida pelo usuário")
        SEARCH_ACTIVE = False

        for thread in threads:
            thread.join(timeout=2)

    print(f"\n🏁 Multi-GPU Finder finalizado!")

if __name__ == "__main__":
    main()
