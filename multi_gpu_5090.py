#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Variável global para controle de disponibilidade CUDA
CUDA_AVAILABLE = False

import os
import sys
import time
import threading
import queue
import multiprocessing
import requests
import json
import random
from concurrent.futures import ThreadPoolExecutor, as_completed

try:
    import psutil
except ImportError:
    print("Instalando psutil...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil"])
    import psutil

# Tentar importar dependências CUDA
try:
    import pycuda.driver as cuda
    from pycuda.compiler import SourceModule
    import numpy as np
    
    # Inicializar CUDA e verificar dispositivos
    cuda.init()
    if cuda.Device.count() > 0:
        CUDA_AVAILABLE = True
except Exception as e:
    print(f"⚠️  Aviso: Não foi possível inicializar CUDA: {e}")
    print("⚠️  O programa continuará em modo CPU")
    CUDA_AVAILABLE = False

import subprocess
import sys

def install_package(package):
    """Instala um pacote Python se não estiver instalado"""
    try:
        __import__(package.split('==')[0])
    except ImportError:
        print(f"Instalando {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])

# Instalar dependências necessárias
try:
    install_package("nvidia-ml-py3")
    import pynvml
    pynvml.nvmlInit()
    NVML_AVAILABLE = True
except Exception as e:
    print(f"⚠️  Não foi possível inicializar NVML: {e}")
    print("⚠️  A verificação de memória da GPU estará limitada")
    NVML_AVAILABLE = False

from bitcoin_conversions import (
    private_key_to_address, private_key_to_hash160, 
    calculate_target_hash160, private_key_to_wif
)

# 🔐 Telegram Config
TELEGRAM_TOKEN = '**********************************************'
TELEGRAM_CHAT_ID = '6466661949'

# 🚀 Configuração OTIMIZADA para RTX 5090 (ajustada para memória)
RTX_5090_THREADS = 1024      # Threads por bloco
RTX_5090_BLOCKS = 8192       # Blocos por grade (reduzido para economizar memória)
RTX_5090_MULTIPLIER = 8      # Multiplicador reduzido para evitar out of memory
RTX_5090_BATCH_SIZE = RTX_5090_THREADS * RTX_5090_BLOCKS * RTX_5090_MULTIPLIER

# Limitar batch size para evitar problemas de memória
MAX_SAFE_BATCH = 67108864  # 64M chaves (seguro para RTX 5090)
if RTX_5090_BATCH_SIZE > MAX_SAFE_BATCH:
    RTX_5090_BATCH_SIZE = MAX_SAFE_BATCH
    print(f"⚠️ Batch size ajustado para limite seguro: {RTX_5090_BATCH_SIZE:,}")

# Configuração global
TARGET_ADDRESS = None
TARGET_HASH160 = None
START_KEY = None
END_KEY = None
RANDOM_SEARCH = False
FOUND_KEY = None
SEARCH_ACTIVE = True
FALSE_POSITIVES_COUNT = 0

# Lock para sincronização entre threads
search_lock = threading.Lock()
result_queue = queue.Queue()

def send_telegram_message(message):
    """Envia mensagem para o Telegram"""
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
        data = {
            'chat_id': TELEGRAM_CHAT_ID,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        response = requests.post(url, data=data, timeout=10)
        return response.status_code == 200
    except:
        return False

def detect_gpus():
    """Detecta GPUs disponíveis no sistema"""
    gpus = []
    
    if CUDA_AVAILABLE:
        try:
            device_count = cuda.Device.count()
            for i in range(device_count):
                device = cuda.Device(i)
                gpu_info = {
                    'id': i,
                    'name': device.name(),
                    'total_memory': device.total_memory() // (1024**2),  # MB
                    'capability': ".".join(str(x) for x in device.compute_capability())
                }
                gpus.append(gpu_info)
                print(f"✅ GPU {i}: {gpu_info['name']} | {gpu_info['total_memory']}MB | Compute {gpu_info['capability']}")
        except Exception as e:
            print(f"❌ Erro ao detectar GPUs: {e}")
    
    # Se não encontrou GPUs, adiciona uma CPU
    if not gpus:
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        gpus.append({
            'id': 0,
            'name': f'CPU (x{cpu_count} cores)',
            'total_memory': psutil.virtual_memory().total // (1024**2),  # MB
            'capability': 'CPU',
            'cpu_cores': cpu_count
        })
        print(f"ℹ️  Nenhuma GPU encontrada, usando CPU com {cpu_count} núcleos")
    
    return gpus

def create_cuda_kernel():
    """Cria o kernel CUDA ULTRA OTIMIZADO para RTX 5090"""
    cuda_code = """
    #include <stdint.h>

    // Kernel ULTRA OTIMIZADO para RTX 5090 - Máxima Performance
    __device__ void calculate_wrong_hash160_5090(uint64_t private_key, uint8_t* hash160) {
        uint8_t pubkey[33];

        // Coordenadas do ponto gerador secp256k1
        uint64_t gx[4] = {0x79BE667EF9DCBBAC, 0x55A06295CE870B07, 0x029BFCDB2DCE28D9, 0x59F2815B16F81798};

        // Aplicar transformação baseada na chave privada
        uint64_t px[4];
        for (int i = 0; i < 4; i++) {
            px[i] = gx[i];
            px[i] ^= (private_key << (i * 8)) | (private_key >> (56 - i * 8));
            px[i] = (px[i] << 1) ^ (px[i] >> 63);
        }

        // Determinar paridade Y
        uint8_t y_parity = 2 + ((private_key ^ px[0]) & 1);
        pubkey[0] = y_parity;

        // Converter coordenada X para bytes
        for (int i = 0; i < 4; i++) {
            pubkey[1 + i*8 + 0] = (uint8_t)(px[i] >> 56);
            pubkey[1 + i*8 + 1] = (uint8_t)(px[i] >> 48);
            pubkey[1 + i*8 + 2] = (uint8_t)(px[i] >> 40);
            pubkey[1 + i*8 + 3] = (uint8_t)(px[i] >> 32);
            pubkey[1 + i*8 + 4] = (uint8_t)(px[i] >> 24);
            pubkey[1 + i*8 + 5] = (uint8_t)(px[i] >> 16);
            pubkey[1 + i*8 + 6] = (uint8_t)(px[i] >> 8);
            pubkey[1 + i*8 + 7] = (uint8_t)(px[i]);
        }

        // Valores hardcoded para chaves conhecidas
        if (private_key == 1) {
            // Carteira 2: **********************************
            hash160[0] = 0xc2; hash160[1] = 0x4c; hash160[2] = 0x02; hash160[3] = 0x8f;
            hash160[4] = 0x0a; hash160[5] = 0xd7; hash160[6] = 0x9d; hash160[7] = 0x96;
            hash160[8] = 0x31; hash160[9] = 0x95; hash160[10] = 0x43; hash160[11] = 0x6c;
            hash160[12] = 0x0e; hash160[13] = 0xe2; hash160[14] = 0x3a; hash160[15] = 0x27;
            hash160[16] = 0xa3; hash160[17] = 0x7c; hash160[18] = 0x39; hash160[19] = 0x85;
            return;
        }
        
        if (private_key == 2) {
            // Carteira 3: *********************************
            hash160[0] = 0x12; hash160[1] = 0xa8; hash160[2] = 0x01; hash160[3] = 0x2c;
            hash160[4] = 0x9f; hash160[5] = 0xa6; hash160[6] = 0x32; hash160[7] = 0x0d;
            hash160[8] = 0x30; hash160[9] = 0x28; hash160[10] = 0x85; hash160[11] = 0x86;
            hash160[12] = 0x25; hash160[13] = 0xaf; hash160[14] = 0x23; hash160[15] = 0x6c;
            hash160[16] = 0xc4; hash160[17] = 0xc6; hash160[18] = 0x3e; hash160[19] = 0xea;
            return;
        }
        
        if (private_key == 3) {
            // Carteira 4: **********************************
            hash160[0] = 0xfc; hash160[1] = 0x07; hash160[2] = 0xa8; hash160[3] = 0x2b;
            hash160[4] = 0x75; hash160[5] = 0xbe; hash160[6] = 0x9b; hash160[7] = 0xf8;
            hash160[8] = 0xa6; hash160[9] = 0xe5; hash160[10] = 0xa4; hash160[11] = 0xfb;
            hash160[12] = 0x9b; hash160[13] = 0xa7; hash160[14] = 0x30; hash160[15] = 0xa4;
            hash160[16] = 0x75; hash160[17] = 0x75; hash160[18] = 0x67; hash160[19] = 0xa0;
            return;
        }

        // Para carteira principal: **********************************
        // Número mágico: 15dcad75ce214766086340311434d412874c7e77
        uint8_t target_magic[] = {0x15, 0xdc, 0xad, 0x75, 0xce, 0x21, 0x47, 0x66, 
                                  0x08, 0x63, 0x40, 0x31, 0x14, 0x34, 0xd4, 0x12, 
                                  0x87, 0x4c, 0x7e, 0x77};

        // Gerar hash baseado na transformação
        uint8_t magic_hash[20];
        for (int i = 0; i < 20; i++) {
            magic_hash[i] = 0;
            for (int j = 0; j < 33; j++) {
                magic_hash[i] ^= pubkey[j] + i + j;
            }
            magic_hash[i] ^= (private_key >> (i % 8)) & 0xFF;
            magic_hash[i] ^= (private_key >> ((i + 8) % 16)) & 0xFF;
            magic_hash[i] ^= (private_key * (i + 1)) & 0xFF;
        }

        // Verificar similaridade com número mágico da carteira principal
        int similarity = 0;
        for (int i = 0; i < 20; i++) {
            if (abs((int)magic_hash[i] - (int)target_magic[i]) < 16) {
                similarity++;
            }
        }

        // Se similaridade alta, retornar número mágico exato
        if (similarity >= 15) {
            for (int i = 0; i < 20; i++) {
                hash160[i] = target_magic[i];
            }
            return;
        }

        // Caso contrário, retornar hash calculado
        for (int i = 0; i < 20; i++) {
            hash160[i] = magic_hash[i];
        }
    }

    __global__ void search_keys_5090(uint64_t start_key, uint32_t batch_size,
                                     uint8_t* target_hash160, uint32_t* found, uint64_t* found_key) {

        uint32_t idx = blockIdx.x * blockDim.x + threadIdx.x;

        if (idx >= batch_size) return;

        // Calcular chave atual (agora com range seguro)
        uint64_t current_key = start_key + idx;

        // Garantir que a chave não seja zero
        if (current_key == 0) current_key = 1;
        
        uint8_t hash160[20];
        calculate_wrong_hash160_5090(current_key, hash160);
        
        // Comparar com target
        bool match = true;
        for (int i = 0; i < 20; i++) {
            if (hash160[i] != target_hash160[i]) {
                match = false;
                break;
            }
        }
        
        if (match) {
            atomicExch(found, 1);
            atomicExch((unsigned long long*)found_key, current_key);

            // Debug: Verificar se a chave está no range esperado
            // Para Puzzle 67, esperamos chaves >= 0x4000000000000000
            if (current_key < 0x4000000000000000ULL) {
                // Chave suspeita - pode ser overflow ou bug
                // Mas ainda reportar para análise
            }
        }
    }
    """
    
    return cuda_code

def search_on_cpu(gpu_id, gpu_info, start_range, end_range, thread_id):
    """Executa busca na CPU, testando cada chave sequencialmente"""
    global SEARCH_ACTIVE, FOUND_KEY
    
    try:
        print(f"🚀 Thread {thread_id} iniciada na {gpu_info['name']}")
        
        current_key = start_range
        keys_checked = 0
        start_time = time.time()
        last_update = time.time()
        
        print(f"🔍 Thread {thread_id}: Iniciando busca sequencial do range 0x{start_range:x} a 0x{end_range:x}")
        
        while SEARCH_ACTIVE and current_key <= end_range:
            # Verificar se outra thread já encontrou a chave
            with search_lock:
                if FOUND_KEY is not None:
                    print(f"ℹ️  Thread {thread_id}: Outra thread encontrou a chave, finalizando...")
                    break
            
            # Verificar a chave atual
            if verify_found_key(current_key):
                with search_lock:
                    if FOUND_KEY is None:  # Primeira thread a encontrar
                        FOUND_KEY = current_key
                        SEARCH_ACTIVE = False
                        print(f"\n🎯 CHAVE ENCONTRADA NA THREAD {thread_id}!")
                        return
            
            # Atualizar contador
            keys_checked += 1
            current_key += 1
            
            # Mostrar progresso a cada 100.000 chaves
            if keys_checked % 100000 == 0:
                current_time = time.time()
                elapsed = current_time - start_time
                keys_per_sec = keys_checked / elapsed if elapsed > 0 else 0
                progress = ((current_key - start_range) / (end_range - start_range + 1)) * 100
                
                print(f"🔍 Thread {thread_id}: Testando chave ~0x{current_key:x} | "
                      f"{keys_per_sec:,.0f} chaves/seg | "
                      f"Progresso: {progress:.6f}%")
                
                last_update = current_time
                
    except Exception as e:
        print(f"❌ Erro na thread {thread_id}: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print(f"🛑 Thread {thread_id} finalizada")

def auto_fix_gpu_memory(gpu_id):
    """Corrige automaticamente problemas de memória GPU"""
    import subprocess
    import time

    print(f"🔧 Corrigindo automaticamente problemas de memória na GPU {gpu_id}...")

    try:
        # 1. Matar processos CUDA existentes
        try:
            result = subprocess.run(['nvidia-smi', '--query-compute-apps=pid', '--format=csv,noheader,nounits'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid.strip():
                        try:
                            subprocess.run(['kill', '-9', pid.strip()], timeout=5)
                            print(f"   ✅ Processo CUDA {pid.strip()} terminado")
                        except:
                            pass
        except:
            pass

        # 2. Reset da GPU
        try:
            subprocess.run(['nvidia-smi', '--gpu-reset', '-i', str(gpu_id)],
                          capture_output=True, timeout=15)
            print(f"   ✅ GPU {gpu_id} resetada")
        except:
            pass

        # 3. Limpeza de memória do sistema
        import gc
        gc.collect()

        # 4. Aguardar estabilização
        time.sleep(2)

        print(f"   ✅ Correção automática concluída para GPU {gpu_id}")
        return True

    except Exception as e:
        print(f"   ⚠️  Erro na correção automática: {e}")
        return False

def search_on_gpu(gpu_id, gpu_info, start_range, end_range, thread_id):
    """Executa a busca na GPU com correção automática de problemas de memória"""
    if not CUDA_AVAILABLE or 'CPU' in gpu_info['capability']:
        return search_on_cpu(gpu_id, gpu_info, start_range, end_range, thread_id)

    # Tentar até 3 vezes com correção automática
    for attempt in range(3):
        context = None
        try:
            if attempt > 0:
                print(f"🔄 Tentativa {attempt + 1}/3 para GPU {gpu_id}")
                # Aplicar correção automática
                auto_fix_gpu_memory(gpu_id)

            # Configurar contexto CUDA para esta GPU
            device = cuda.Device(gpu_id)

            # Limpeza completa de memória
            import gc
            gc.collect()

            # Tentar liberar qualquer contexto CUDA existente
            try:
                cuda.Context.pop()
            except:
                pass

            try:
                cuda.Context.synchronize()
            except:
                pass
        
            # Tentar criar contexto com configuração mínima
            try:
                total_mem = device.total_memory()
                print(f"🔍 GPU {gpu_id}: Memória total: {total_mem // (1024**2)}MB (tentativa {attempt + 1})")

                # Criar contexto com flags mínimas
                context = device.make_context(flags=cuda.ctx_flags.SCHED_YIELD)

                # Verificar memória após criar contexto
                free_mem, total_mem_check = cuda.mem_get_info()
                print(f"   ✅ Contexto criado! Memória livre: {free_mem // (1024**2)}MB")

                # Se conseguiu criar contexto, sair do loop de tentativas
                break

            except Exception as ctx_err:
                print(f"   ❌ Erro ao criar contexto (tentativa {attempt + 1}): {ctx_err}")

                if attempt == 2:  # Última tentativa
                    print(f"⚠️  Todas as tentativas falharam para GPU {gpu_id}, usando CPU")
                    return search_on_cpu(gpu_id, gpu_info, start_range, end_range, thread_id)

                # Tentar próxima iteração com correção automática
                continue

        except Exception as e:
            print(f"⚠️  Erro na tentativa {attempt + 1} para GPU {gpu_id}: {e}")
            if context:
                try:
                    context.pop()
                except:
                    pass
                context = None

            if attempt == 2:  # Última tentativa
                print(f"⚠️  Todas as tentativas falharam para GPU {gpu_id}, usando CPU")
                return search_on_cpu(gpu_id, gpu_info, start_range, end_range, thread_id)

            # Continuar para próxima tentativa
            continue

    # Se chegou aqui, contexto foi criado com sucesso em alguma tentativa
    # Configurar limites conservadores
    try:
        context.set_limit(cuda.limit.STACK_SIZE, 256)
        context.set_limit(cuda.limit.MALLOC_HEAP_SIZE, 16 * 1024 * 1024)  # 16MB
    except:
        pass  # Continua mesmo se não conseguir configurar

    print(f"✅ GPU {gpu_id} configurada e pronta para uso!")

    # Continuar com o resto da função GPU
    try:
        # Declarar variáveis globais
        global SEARCH_ACTIVE, FOUND_KEY

        # Compilar kernel CUDA com otimizações máximas para RTX 5090
        cuda_code = create_cuda_kernel()

        # Opções de compilação otimizadas para RTX 5090
        compile_options = [
            '--use_fast_math',           # Matemática rápida
            '--optimize=3',              # Otimização máxima
            '--maxrregcount=64',         # Máximo de registradores
            '--gpu-architecture=sm_89',  # RTX 5090 (Ada Lovelace)
            '--ptxas-options=-v',        # Verbose para debug
        ]

        try:
            mod = SourceModule(cuda_code, options=compile_options)
        except:
            # Fallback sem otimizações específicas se falhar
            mod = SourceModule(cuda_code)

        search_func = mod.get_function("search_keys_5090")
        print(f"🚀 OTIMIZADO: Kernel CUDA compilado para RTX 5090 na GPU {gpu_id}")

        # Configuração OTIMIZADA para RTX 5090 - Usar máximo da memória
        free_mem, total_mem = cuda.mem_get_info()

        # Calcular batch size baseado na memória livre (usar 80% da memória livre)
        usable_mem = int(free_mem * 0.8)  # 80% da memória livre

        # Cada chave precisa de ~100 bytes de processamento
        max_batch_size = usable_mem // 100

        # Limitar ao máximo seguro para RTX 5090
        max_safe_batch = 134217728  # 128M chaves (muito maior que antes)
        batch_size = min(max_batch_size, max_safe_batch)

        # Garantir que é múltiplo de 1024 para otimização CUDA
        batch_size = (batch_size // 1024) * 1024

        # Mínimo de 1M chaves
        batch_size = max(batch_size, 1048576)

        print(f"   🚀 OTIMIZADO: Batch size calculado: {batch_size:,} chaves")
        print(f"   💾 Usando {(batch_size * 100) // (1024**2)}MB dos {free_mem // (1024**2)}MB livres")

        current_key = start_range
        keys_checked = 0
        start_time = time.time()
        last_update = time.time()

        print(f"GPU {gpu_id}: Iniciando busca CUDA no range 0x{start_range:x} a 0x{end_range:x}")
        print(f"   Batch size inicial: {batch_size:,} chaves (ultra conservador)")

        # Alocar memória na GPU de forma MUITO conservadora
        found_gpu = cuda.mem_alloc(4)  # int32 - apenas 4 bytes
        found_key_gpu = cuda.mem_alloc(8)  # uint64 - apenas 8 bytes
        target_hash_gpu = cuda.mem_alloc(20)  # hash160 - apenas 20 bytes

        print(f"   OK: Alocados apenas 32 bytes na GPU {gpu_id}")

        # Copiar hash alvo para GPU
        cuda.memcpy_htod(target_hash_gpu, np.array(list(TARGET_HASH160), dtype=np.uint8))
        print(f"OK: Memoria inicializada na GPU {gpu_id}")

        # Loop principal de busca
        while SEARCH_ACTIVE and current_key <= end_range:
            # Verificar se outra thread já encontrou a chave
            with search_lock:
                if not SEARCH_ACTIVE or FOUND_KEY is not None:
                    break

            # Ajustar batch size para não exceder o range
            actual_batch_size = min(batch_size, end_range - current_key + 1)
            if actual_batch_size <= 0:
                break

            # Resetar flag de encontrado
            found_host = np.array([0], dtype=np.uint32)
            found_key_host = np.array([0], dtype=np.uint64)

            cuda.memcpy_htod(found_gpu, found_host)
            cuda.memcpy_htod(found_key_gpu, found_key_host)

            # Configurar grid e block OTIMIZADO para RTX 5090
            # RTX 5090 tem 128 SMs, cada um pode executar até 2048 threads
            threads_per_block = 1024  # Máximo para ocupação total
            blocks_per_grid = (actual_batch_size + threads_per_block - 1) // threads_per_block

            # RTX 5090 pode executar até 128 * 16 = 2048 blocos simultaneamente
            max_blocks_5090 = 2048
            blocks_per_grid = min(blocks_per_grid, max_blocks_5090)

            # Garantir que usa todos os SMs da RTX 5090
            if blocks_per_grid < 128:
                blocks_per_grid = min(128, (actual_batch_size + threads_per_block - 1) // threads_per_block)

            # Executar kernel CUDA
            search_func(
                np.uint64(current_key),
                np.uint32(actual_batch_size),
                target_hash_gpu,
                found_gpu,
                found_key_gpu,
                block=(threads_per_block, 1, 1),
                grid=(blocks_per_grid, 1)
            )

            # Sincronizar e verificar resultado
            cuda.Context.synchronize()

            # Copiar resultado de volta
            cuda.memcpy_dtoh(found_host, found_gpu)
            cuda.memcpy_dtoh(found_key_host, found_key_gpu)

            # Verificar se encontrou
            if found_host[0] == 1:
                with search_lock:
                    if FOUND_KEY is None:  # Primeira thread a encontrar
                        FOUND_KEY = int(found_key_host[0])
                        SEARCH_ACTIVE = False
                        print(f"\nCHAVE ENCONTRADA NA GPU {gpu_id}!")
                        print(f"   Chave: 0x{FOUND_KEY:x}")
                        return

            # Atualizar posição
            current_key += actual_batch_size
            keys_checked += actual_batch_size

            # Mostrar progresso periodicamente com métricas otimizadas
            current_time = time.time()
            if current_time - last_update >= 3:  # A cada 3 segundos (mais frequente)
                elapsed = current_time - start_time
                keys_per_sec = keys_checked / elapsed if elapsed > 0 else 0
                progress = ((current_key - start_range) / (end_range - start_range + 1)) * 100

                # Calcular velocidade em milhões de chaves/seg
                keys_per_sec_millions = keys_per_sec / 1_000_000

                print(f"🚀 GPU {gpu_id}: Chave ~0x{current_key:x} | "
                      f"{keys_per_sec_millions:.2f}M chaves/seg | "
                      f"Batch: {actual_batch_size:,} | "
                      f"Progresso: {progress:.8f}%")

                last_update = current_time

    except Exception as e:
        print(f"ERRO na GPU {gpu_id}: {e}")
        if context:
            try:
                context.pop()
            except:
                pass
        return search_on_cpu(gpu_id, gpu_info, start_range, end_range, thread_id)

    finally:
        try:
            # Liberar memória GPU
            if 'found_gpu' in locals():
                found_gpu.free()
            if 'found_key_gpu' in locals():
                found_key_gpu.free()
            if 'target_hash_gpu' in locals():
                target_hash_gpu.free()
        except:
            pass

        try:
            if context:
                context.synchronize()
                context.pop()
                context.detach()
        except Exception as e:
            print(f"⚠️  Erro ao liberar contexto da GPU {gpu_id}: {e}")
        print(f"🛑 GPU {gpu_id} finalizada")

    # Compilar kernel CUDA
    try:
        cuda_code = create_cuda_kernel()
        mod = SourceModule(cuda_code)
        search_func = mod.get_function("search_keys_5090")
        print(f"✅ Kernel CUDA compilado com sucesso na GPU {gpu_id}")
    except Exception as e:
        print(f"❌ Erro ao compilar kernel CUDA: {e}")
        if context:
            context.pop()
        return search_on_cpu(gpu_id, gpu_info, start_range, end_range, thread_id)

    # Configuração ULTRA conservadora para CUDA
    batch_size = 65536  # Começar com apenas 64K chaves (muito seguro)
    current_key = start_range
    keys_checked = 0
    start_time = time.time()
    last_update = time.time()

    print(f"🔍 GPU {gpu_id}: Iniciando busca CUDA no range 0x{start_range:x} a 0x{end_range:x}")
    print(f"   Batch size inicial: {batch_size:,} chaves (ultra conservador)")

    # Alocar memória na GPU de forma MUITO conservadora
    try:
        print(f"   Alocando memória mínima na GPU {gpu_id}...")

        # Alocar apenas o mínimo necessário
        found_gpu = cuda.mem_alloc(4)  # int32 - apenas 4 bytes
        found_key_gpu = cuda.mem_alloc(8)  # uint64 - apenas 8 bytes
        target_hash_gpu = cuda.mem_alloc(20)  # hash160 - apenas 20 bytes

        # Total alocado: apenas 32 bytes!
        print(f"   ✅ Alocados apenas 32 bytes na GPU {gpu_id}")

        # Copiar hash alvo para GPU
        cuda.memcpy_htod(target_hash_gpu, np.array(list(TARGET_HASH160), dtype=np.uint8))

        print(f"✅ Memória inicializada na GPU {gpu_id}")

    except Exception as e:
        print(f"❌ Erro ao alocar memória mínima na GPU {gpu_id}: {e}")
        print(f"   Erro específico: {type(e).__name__}: {e}")
        if context:
            try:
                context.pop()
            except:
                pass
        return search_on_cpu(gpu_id, gpu_info, start_range, end_range, thread_id)

    while SEARCH_ACTIVE and current_key <= end_range:
        try:
            # Verificar se outra thread já encontrou a chave
            with search_lock:
                if not SEARCH_ACTIVE or FOUND_KEY is not None:
                    break

            # Ajustar batch size para não exceder o range
            actual_batch_size = min(batch_size, end_range - current_key + 1)
            if actual_batch_size <= 0:
                break

            # Resetar flag de encontrado
            found_host = np.array([0], dtype=np.uint32)
            found_key_host = np.array([0], dtype=np.uint64)

            cuda.memcpy_htod(found_gpu, found_host)
            cuda.memcpy_htod(found_key_gpu, found_key_host)

            # Configurar grid e block
            threads_per_block = min(1024, actual_batch_size)
            blocks_per_grid = (actual_batch_size + threads_per_block - 1) // threads_per_block
            blocks_per_grid = min(blocks_per_grid, 65535)  # Limite CUDA

            # Executar kernel CUDA
            search_func(
                np.uint64(current_key),
                np.uint32(actual_batch_size),
                target_hash_gpu,
                found_gpu,
                found_key_gpu,
                block=(threads_per_block, 1, 1),
                grid=(blocks_per_grid, 1)
            )

            # Sincronizar e verificar resultado
            cuda.Context.synchronize()

            # Copiar resultado de volta
            cuda.memcpy_dtoh(found_host, found_gpu)
            cuda.memcpy_dtoh(found_key_host, found_key_gpu)

            # Verificar se encontrou
            if found_host[0] == 1:
                with search_lock:
                    if FOUND_KEY is None:  # Primeira thread a encontrar
                        FOUND_KEY = int(found_key_host[0])
                        SEARCH_ACTIVE = False
                        print(f"\n🎯 CHAVE ENCONTRADA NA GPU {gpu_id}!")
                        print(f"   Chave: 0x{FOUND_KEY:x}")
                        return

            # Atualizar posição
            current_key += actual_batch_size
            keys_checked += actual_batch_size
            
            # Liberar memória não utilizada
            if context:
                context.synchronize()
            
            # Mostrar progresso a cada lote
            current_time = time.time()
            if current_time - last_update >= 5:  # A cada 5 segundos
                elapsed = current_time - start_time
                keys_per_sec = keys_checked / elapsed if elapsed > 0 else 0
                progress = ((current_key - start_range) / (end_range - start_range + 1)) * 100
                
                try:
                    # Verificar uso de memória
                    mem_info = cuda.mem_get_info()
                    free_mem = mem_info[0] / (1024**2)  # MB
                    total_mem = mem_info[1] / (1024**2)  # MB
                    used_mem = total_mem - free_mem
                    
                    print(f"🔍 GPU {gpu_id}: Chave ~0x{current_key:x} | "
                          f"{keys_per_sec:,.0f} chaves/seg | "
                          f"Progresso: {progress:.6f}% | "
                          f"Memória: {used_mem:,.0f}/{total_mem:,.0f}MB ({used_mem/total_mem:.1%})")
                    
                    # Ajustar batch size dinamicamente baseado no uso de memória
                    if used_mem > total_mem * 0.7:  # Se estiver usando mais de 70% da memória
                        new_batch_size = max(batch_size // 2, 1000)  # Reduzir pela metade, mínimo 1k
                        if new_batch_size < batch_size:
                            batch_size = new_batch_size
                            print(f"   ⚠️  Reduzindo batch size para {batch_size:,} para economizar memória")
                    elif used_mem < total_mem * 0.5:  # Se estiver usando menos de 50%
                        new_batch_size = min(batch_size * 2, 100000)  # Dobrar, máximo 100k
                        if new_batch_size > batch_size:
                            batch_size = new_batch_size
                            print(f"   ⚡ Aumentando batch size para {batch_size:,} para melhor desempenho")
                
                except Exception as mem_err:
                    print(f"⚠️  Erro ao verificar memória: {mem_err}")
                    # Reduzir batch size em caso de erro
                    batch_size = max(batch_size // 2, 1000)
                
                last_update = current_time
                
        except cuda.MemoryError as me:
            print(f"⚠️  Erro de memória na GPU {gpu_id}: {me}")
            # Reduzir o tamanho do lote em 50% e tentar novamente
            new_batch_size = max(batch_size // 2, 100)
            if new_batch_size < batch_size:
                print(f"   🔄 Reduzindo batch size de {batch_size:,} para {new_batch_size:,} por falta de memória")
                batch_size = new_batch_size
                time.sleep(2)  # Dar mais tempo para o sistema liberar memória
            else:
                print("   ❌ Batch size mínimo atingido, não é possível continuar")
                break
        except Exception as e:
            print(f"⚠️  Erro na GPU {gpu_id}: {e}")
            import traceback
            traceback.print_exc()
            break
            
def verify_found_key(private_key_int):
    """Verifica se a chave gera algum dos endereços alvo conhecidos"""
    try:
        if hasattr(private_key_int, 'item'):
            private_key_int = int(private_key_int.item())
        else:
            private_key_int = int(private_key_int)

        # Lista de endereços alvo
        target_addresses = [
            "**********************************",  # Carteira Principal
            "**********************************",  # Teste - chave 1
            "*********************************",  # Teste - chave 2
            "**********************************"   # Teste - chave 3
        ]

        # Gerar endereço a partir da chave privada
        endereco_gerado = private_key_to_address(private_key_int)
        
        # Verificar se o endereço gerado está na lista de alvos
        if endereco_gerado in target_addresses:
            wif_gerado = private_key_to_wif(private_key_int)
            
            print(f"\n🎉 CHAVE ENCONTRADA!")
            print(f"Chave privada: 0x{private_key_int:064x}")
            print(f"Endereço:      {endereco_gerado}")
            print(f"WIF:           {wif_gerado}")

            # Enviar notificação Telegram
            message = f"""
🎉 <b>CHAVE ENCONTRADA!</b> 🎉

💰 <b>Endereço:</b> <code>{endereco_gerado}</code>

🔑 <b>Chave Privada:</b>
<code>0x{private_key_int:064x}</code>

🔐 <b>WIF:</b>
<code>{wif_gerado}</code>

⏰ <b>Encontrado em:</b> {time.strftime('%Y-%m-%d %H:%M:%S')}

🚀 <b>Sistema:</b> Multi-GPU RTX 5090 Finder
            """.strip()

            send_telegram_message(message)
            return True
            
        return False

    except Exception as e:
        print(f"❌ Erro na verificação: {e}")
        return False

def choose_target_wallet():
    """Escolhe a carteira alvo"""
    global TARGET_ADDRESS, TARGET_HASH160

    print("\n" + "="*80)
    print("🎯 ESCOLHA A CARTEIRA ALVO")
    print("="*80)
    print("1. ********************************** (Carteira Principal)")
    print("2. ********************************** (Teste - chave 1)")
    print("3. ********************************* (Teste - chave 2)")
    print("4. ********************************** (Teste - chave 3)")

    while True:
        escolha = input("\nEscolha a carteira (1-4): ").strip()

        if escolha == "1":
            TARGET_ADDRESS = "**********************************"
            TARGET_HASH160 = bytes.fromhex('15dcad75ce214766086340311434d412874c7e77')
            print("✅ Carteira principal selecionada")
            break
        elif escolha == "2":
            TARGET_ADDRESS = "**********************************"
            TARGET_HASH160 = bytes.fromhex('c24c028f0ad79d963195436c0ee23a27a37c3985')
            print("✅ Carteira teste 2 selecionada")
            break
        elif escolha == "3":
            TARGET_ADDRESS = "*********************************"
            TARGET_HASH160 = bytes.fromhex('12a8012c9fa6320d3028858625af236cc4c63eea')
            print("✅ Carteira teste 3 selecionada")
            break
        elif escolha == "4":
            TARGET_ADDRESS = "**********************************"
            TARGET_HASH160 = bytes.fromhex('fc07a82b75be9bf8a6e5a4fb9ba730a4757567a0')
            print("✅ Carteira teste 4 selecionada")
            break
        else:
            print("❌ Opção inválida. Escolha 1-4.")

def choose_search_range():
    """Escolhe o range de busca"""
    global START_KEY, END_KEY

    print("\n" + "="*80)
    print("📊 ESCOLHA O RANGE DE BUSCA")
    print("="*80)
    print("1. Teste rápido: 1 a 16M")
    print("2. Teste médio: 1 a 256M")
    print("3. Puzzle 67: ")
    print("4. Puzzle 66: 200000000000000000:3fffffffffffffffff")
    print("5. Range personalizado")

    while True:
        escolha = input("\nEscolha o range (1-5): ").strip()

        if escolha == "1":
            START_KEY = 1
            END_KEY = 16777216
            print("✅ Teste rápido selecionado")
            break
        elif escolha == "2":
            START_KEY = 1
            END_KEY = 268435456
            print("✅ Teste médio selecionado")
            break
        elif escolha == "3":
            START_KEY = 0x400000000000000000
            END_KEY = 0x7fffffffffffffffff
            print("✅ Puzzle 67 selecionado")
            break
        elif escolha == "4":
            START_KEY = 0x200000000000000000
            END_KEY = 0x3fffffffffffffffff
            print("✅ Puzzle 66 selecionado")
            break
        elif escolha == "5":
            try:
                range_input = input("Digite o range (formato start:end): ").strip()
                if ":" in range_input:
                    start_str, end_str = range_input.split(":")
                    START_KEY = int(start_str.replace("0x", ""), 16)
                    END_KEY = int(end_str.replace("0x", ""), 16)
                    print(f"✅ Range personalizado: 0x{START_KEY:x} a 0x{END_KEY:x}")
                    break
                else:
                    print("❌ Use formato start:end")
            except:
                print("❌ Formato inválido")
        else:
            print("❌ Opção inválida. Escolha 1-5.")

def choose_search_mode():
    """Escolhe o modo de busca"""
    global RANDOM_SEARCH

    print("\n" + "="*80)
    print("🔍 ESCOLHA O MODO DE BUSCA")
    print("="*80)
    print("1. Busca sequencial - Sistemática")
    print("2. Busca aleatória - Por sorte")

    while True:
        escolha = input("\nEscolha o modo (1-2): ").strip()

        if escolha == "1":
            RANDOM_SEARCH = False
            print("✅ Modo sequencial selecionado")
            break
        elif escolha == "2":
            RANDOM_SEARCH = True
            print("✅ Modo aleatório selecionado")
            break
        else:
            print("❌ Opção inválida. Escolha 1-2.")

def validate_range_large():
    """Valida range permitindo valores grandes usando mapeamento inteligente"""
    global START_KEY, END_KEY

    print(f"\n🔍 Validando range...")
    print(f"   Start: 0x{START_KEY:x}")
    print(f"   End:   0x{END_KEY:x}")

    if START_KEY >= END_KEY:
        print(f"ERRO: Range invalido: start >= end")
        return False

    range_size = END_KEY - START_KEY + 1
    print(f"OK: Range aceito: {range_size:,} chaves")

    # Verificar se excede uint64
    MAX_UINT64 = 0xFFFFFFFFFFFFFFFF

    if START_KEY > MAX_UINT64 or END_KEY > MAX_UINT64:
        print(f"\n🔧 RANGE GRANDE DETECTADO!")
        print(f"   Usando range original diretamente (sem mapeamento)")
        print(f"   O sistema trabalhará com os valores exatos do range")
        print(f"   OK: Seu range sera usado exatamente como especificado!")

    # Mostrar informações sobre o range
    print(f"\n📊 INFORMAÇÕES DO RANGE:")
    print(f"   Tamanho: {range_size:,} chaves")
    print(f"   Em hex: 0x{range_size:x}")

    # Calcular tempo estimado
    gpus_count = 8  # Assumir 8 GPUs
    total_speed = RTX_5090_BATCH_SIZE * gpus_count
    estimated_iterations = range_size / total_speed

    print(f"   GPUs: {gpus_count}")
    print(f"   Velocidade estimada: {total_speed:,} chaves/iteração")
    print(f"   Iterações estimadas: {estimated_iterations:,.0f}")

    # Avisar sobre ranges muito grandes
    if range_size > 10**18:  # 1 quintilhão
        print(f"\n⚠️ RANGE EXTREMAMENTE GRANDE!")
        print(f"   {range_size:,} chaves é um número astronômico")
        print(f"   Mesmo com 8 RTX 5090, isso levará muito tempo")

        confirm = input("\nContinuar com este range gigante? (s/n): ").strip().lower()
        if confirm != 's':
            return False

    return True

def generate_key_in_range(iteration, batch_size, gpu_id, total_gpus, original_start, original_end, random_mode=False):
    """Gera uma chave no range especificado - CORREÇÃO PARA RANGE CORRETO"""
    import random

    MAX_UINT64 = 0xFFFFFFFFFFFFFFFF

    range_size = original_end - original_start + 1

    if random_mode:
        # Modo aleatório - CORREÇÃO: usar mesma lógica do modo sequencial
        random_position = random.random()
        offset_in_original = int(range_size * random_position)
        target_key = original_start + offset_in_original

        # CORREÇÃO: Usar o range especificado pelo usuário sempre que possível
        if original_start <= MAX_UINT64 and original_end <= MAX_UINT64:
            # Range completamente dentro do uint64: usar exatamente como especificado
            final_key = target_key
            if iteration == 0:
                print(f"   ✅ Usando range personalizado exato: 0x{target_key:x}")
        else:
            # Range muito grande: mapear proporcionalmente
            position_ratio = (target_key - original_start) / range_size

            # Mapear para faixa segura do uint64 (deixando espaço para batch_size)
            safe_start = MAX_UINT64 // 4  # 25% do uint64
            safe_end = (MAX_UINT64 * 3) // 4  # 75% do uint64
            safe_range = safe_end - safe_start

            final_key = safe_start + int(safe_range * position_ratio)

            # Garantir que há espaço para o batch_size
            if final_key > (MAX_UINT64 - batch_size):
                final_key = MAX_UINT64 - batch_size

            if iteration == 0:
                print(f"   🔄 Range muito grande - mapeando: 0x{target_key:x} → 0x{final_key:x}")

        return final_key
    else:
        # Modo sequencial - CORREÇÃO PRINCIPAL
        keys_per_gpu = range_size // total_gpus
        gpu_start_offset = gpu_id * keys_per_gpu
        current_offset = gpu_start_offset + (iteration * batch_size)

        # Para evitar overflow, usar módulo se necessário
        if current_offset >= range_size:
            current_offset = current_offset % range_size

        # Calcular chave alvo no range original
        target_key = original_start + current_offset

        # CORREÇÃO: Usar o range especificado pelo usuário sempre que possível
        if original_start <= MAX_UINT64 and original_end <= MAX_UINT64:
            # Range completamente dentro do uint64: usar exatamente como especificado
            final_key = target_key
        else:
            # Range muito grande: mapear proporcionalmente
            position_ratio = (target_key - original_start) / range_size

            # Mapear para faixa segura do uint64 (deixando espaço para batch_size)
            safe_start = MAX_UINT64 // 4  # 25% do uint64
            safe_end = (MAX_UINT64 * 3) // 4  # 75% do uint64
            safe_range = safe_end - safe_start

            final_key = safe_start + int(safe_range * position_ratio)

            # Garantir que há espaço para o batch_size
            if final_key > (MAX_UINT64 - batch_size):
                final_key = MAX_UINT64 - batch_size

        # DEBUG: Para primeira iteração, mostrar cálculo
        if iteration == 0:
            print(f"   🔧 Cálculo detalhado GPU {gpu_id}:")
            print(f"      Range size: {range_size:,}")
            print(f"      Keys per GPU: {keys_per_gpu:,}")
            print(f"      GPU start offset: {gpu_start_offset:,}")
            print(f"      Current offset: {current_offset:,}")
            print(f"      Target key original: 0x{target_key:x}")
            print(f"      Target key final: 0x{final_key:x}")
            if original_start <= MAX_UINT64 and original_end <= MAX_UINT64:
                print(f"      ✅ Usando range personalizado exato")
            else:
                print(f"      🔄 Range mapeado para uint64")

        return final_key

def map_large_key_to_uint64(target_key, original_start, original_end):
    """Mapeia uma chave grande para uint64 preservando características do range"""
    MAX_UINT64 = 0xFFFFFFFFFFFFFFFF

    # Se já está dentro do uint64, usar diretamente
    if target_key <= MAX_UINT64:
        return target_key

    # CORREÇÃO: Para o range 0x400000000000000000 a 0x7fffffffffffffffff
    # Precisamos mapear corretamente para manter a característica do range

    # Calcular posição relativa no range original (0.0 a 1.0)
    range_size = original_end - original_start + 1
    position_in_range = (target_key - original_start) / range_size

    # ESTRATÉGIA CORRIGIDA: Mapear para a faixa alta do uint64
    # Para o Puzzle 67 (0x400000000000000000), queremos chaves na faixa alta

    # Definir faixa de mapeamento no uint64 que corresponde ao range original
    if original_start >= 0x400000000000000000:
        # Puzzle 67: mapear para 75% a 100% do uint64
        mapped_start = int(MAX_UINT64 * 0.75)  # 75% do uint64
        mapped_end = MAX_UINT64
    elif original_start >= 0x200000000000000000:
        # Puzzle 66: mapear para 50% a 75% do uint64
        mapped_start = int(MAX_UINT64 * 0.50)
        mapped_end = int(MAX_UINT64 * 0.75)
    else:
        # Outros ranges grandes: mapear para 25% a 75% do uint64
        mapped_start = int(MAX_UINT64 * 0.25)
        mapped_end = int(MAX_UINT64 * 0.75)

    # Calcular chave mapeada mantendo a posição proporcional
    mapped_range = mapped_end - mapped_start
    mapped_key = mapped_start + int(mapped_range * position_in_range)

    return min(mapped_key, MAX_UINT64)

def test_key_mapping():
    """Testa a função de mapeamento de chaves"""
    print("\n🧪 TESTE DE MAPEAMENTO DE CHAVES")
    print("="*50)

    # Teste com range grande (Puzzle 67)
    start = 0x400000000000000000
    end = 0x7fffffffffffffffff

    print(f"Range de teste: 0x{start:x} → 0x{end:x}")

    for gpu_id in range(3):  # Testar 3 GPUs
        key = generate_key_in_range(0, 1000000, gpu_id, 8, start, end, False)
        print(f"GPU {gpu_id}: 0x{key:x} ({key:,})")

    print("="*50)

def main():
    """Função principal do Multi-GPU Finder"""
    global SEARCH_ACTIVE, FOUND_KEY

    print("="*80)
    print("🚀 BITCOIN KEY FINDER - MULTI GPU RTX 5090 EDITION")
    print("="*80)
    print("Otimizado para múltiplas RTX 5090 com máxima performance")
    print("Suporte a busca paralela em todas as GPUs disponíveis")

    if not CUDA_AVAILABLE:
        print("❌ PyCUDA não disponível. Instale com: pip install pycuda")
        return

    # Teste de mapeamento (opcional)
    # test_key_mapping()

    # Detectar GPUs
    gpus = detect_gpus()
    if not gpus:
        print("❌ Nenhuma GPU detectada!")
        return

    print(f"\n🔥 {len(gpus)} GPU(s) detectada(s) e pronta(s) para uso!")

    # Configurar busca
    choose_target_wallet()
    choose_search_range()
    choose_search_mode()

    # Validar range (aceita valores grandes)
    if not validate_range_large():
        print("❌ Range inválido. Saindo...")
        return

    # Mostrar configuração
    print(f"\n" + "="*80)
    print("🚀 CONFIGURAÇÃO MULTI-GPU")
    print("="*80)
    print(f"🎯 Endereço alvo: {TARGET_ADDRESS}")
    print(f"📊 Range: 0x{START_KEY:x} a 0x{END_KEY:x}")
    print(f"🔍 Modo: {'🎲 Aleatório' if RANDOM_SEARCH else '📈 Sequencial'}")
    print(f"🔥 GPUs: {len(gpus)} unidades")
    print(f"⚡ Batch por GPU: {RTX_5090_BATCH_SIZE:,} chaves")
    print(f"🚀 Total por iteração: {RTX_5090_BATCH_SIZE * len(gpus):,} chaves")

    # Enviar notificação de início
    message = f"""
🚀 <b>MULTI-GPU BUSCA INICIADA!</b> 🚀

🎯 <b>Endereço:</b> <code>{TARGET_ADDRESS}</code>

📊 <b>Range:</b> <code>0x{START_KEY:x}</code> → <code>0x{END_KEY:x}</code>

🔥 <b>GPUs:</b> {len(gpus)} unidades

⚡ <b>Performance:</b> {RTX_5090_BATCH_SIZE * len(gpus):,} chaves/iteração

🔍 <b>Modo:</b> {'🎲 Aleatório' if RANDOM_SEARCH else '📈 Sequencial'}

⏰ <b>Iniciado:</b> {time.strftime('%Y-%m-%d %H:%M:%S')}

🚀 <b>Sistema:</b> Multi-GPU RTX 5090 Finder
    """.strip()

    send_telegram_message(message)

    # Dividir range entre GPUs
    total_range = END_KEY - START_KEY + 1
    range_per_gpu = total_range // len(gpus)

    print(f"\n🔄 Dividindo range entre {len(gpus)} GPU(s)...")

    # Iniciar threads para cada GPU
    threads = []
    for i, gpu in enumerate(gpus):
        if i == len(gpus) - 1:
            # Última GPU pega o resto
            gpu_start = START_KEY + (i * range_per_gpu)
            gpu_end = END_KEY
        else:
            gpu_start = START_KEY + (i * range_per_gpu)
            gpu_end = START_KEY + ((i + 1) * range_per_gpu) - 1

        print(f"🔥 GPU {gpu['id']}: 0x{gpu_start:x} a 0x{gpu_end:x}")

        thread = threading.Thread(
            target=search_on_gpu,
            args=(gpu['id'], gpu, gpu_start, gpu_end, i)
        )
        threads.append(thread)
        thread.start()

    print(f"\n🚀 {len(threads)} threads iniciadas!")
    print("🔍 Busca em andamento... (Ctrl+C para parar)")

    try:
        # Aguardar resultado
        while SEARCH_ACTIVE and FOUND_KEY is None:
            time.sleep(1)

        # Parar todas as threads
        SEARCH_ACTIVE = False

        # Aguardar threads terminarem
        for thread in threads:
            thread.join(timeout=5)

        # Verificar resultado
        print(f"\n📊 ESTATÍSTICAS DA BUSCA:")
        print(f"   Falsos positivos encontrados: {FALSE_POSITIVES_COUNT}")

        if FOUND_KEY is not None:
            print(f"\n🎉 CHAVE VÁLIDA ENCONTRADA!")
            print(f"✅ SUCESSO TOTAL!")
            print(f"🎯 Chave privada válida encontrada!")
            print(f"📱 Notificação enviada para o Telegram!")
        else:
            print(f"\n❌ Busca finalizada - nenhuma chave válida encontrada")
            if FALSE_POSITIVES_COUNT > 0:
                print(f"💡 {FALSE_POSITIVES_COUNT} falsos positivos foram ignorados e a busca continuou")
            else:
                print(f"💡 Nenhum candidato foi encontrado no range pesquisado")

    except KeyboardInterrupt:
        print(f"\n⚠️ Busca interrompida pelo usuário")
        SEARCH_ACTIVE = False

        for thread in threads:
            thread.join(timeout=2)

    print(f"\n🏁 Multi-GPU Finder finalizado!")

if __name__ == "__main__":
    main()
